import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { User, UserDocument } from './schemas/user.schema';

export interface GoogleUser {
  googleId: string;
  email: string;
  name: string;
  picture?: string;
}

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private jwtService: JwtService,
  ) {}

  async validateGoogleUser(googleUser: GoogleUser): Promise<UserDocument> {
    const { googleId, email, name, picture } = googleUser;

    // Check if user already exists
    let user = await this.userModel.findOne({ googleId }).exec();

    if (!user) {
      // Create new user if doesn't exist
      user = new this.userModel({
        googleId,
        email,
        name,
        picture,
      });
      await user.save();
    } else {
      // Update existing user info
      user.email = email;
      user.name = name;
      user.picture = picture;
      await user.save();
    }

    return user;
  }

  async validateUserById(userId: string): Promise<User | null> {
    return this.userModel.findById(userId).exec();
  }

  async generateJwtToken(user: UserDocument): Promise<string> {
    const payload = {
      email: user.email,
      sub: user._id.toString(),
      name: user.name
    };
    return this.jwtService.sign(payload);
  }

  async getUserProfile(userId: string): Promise<UserDocument | null> {
    return this.userModel.findById(userId).select('-googleId').exec();
  }


}
