import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { User } from '@prisma/client';

export interface GoogleUser {
  googleId: string;
  email: string;
  name: string;
  picture?: string;
}

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}

  async validateGoogleUser(googleUser: GoogleUser): Promise<User> {
    const { googleId, email, name, picture } = googleUser;

    // Check if user already exists
    let user = await this.prisma.user.findUnique({
      where: { googleId }
    });

    if (!user) {
      // Create new user if doesn't exist
      user = await this.prisma.user.create({
        data: {
          googleId,
          email,
          name,
          picture,
        }
      });
    } else {
      // Update existing user info
      user = await this.prisma.user.update({
        where: { id: user.id },
        data: {
          email,
          name,
          picture,
        }
      });
    }

    return user;
  }

  async validateUserById(userId: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { id: userId }
    });
  }

  async generateJwtToken(user: User): Promise<string> {
    const payload = {
      email: user.email,
      sub: user.id,
      name: user.name
    };
    return this.jwtService.sign(payload);
  }

  async getUserProfile(userId: string): Promise<Omit<User, 'googleId'> | null> {
    return this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        picture: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    });
  }


}
