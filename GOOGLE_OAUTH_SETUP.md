# Google OAuth Setup Guide

## Quick Fix for OAuth2Strategy Error

The error `OAuth2Strategy requires a clientID option` means your Google OAuth credentials are not configured. Follow these steps:

### Step 1: Get Google OAuth Credentials

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Create or Select Project**: Create a new project or select an existing one
3. **Enable Google+ API**:
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it
4. **Create OAuth 2.0 Credentials**:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Add authorized redirect URIs:
     - `http://localhost:3001/auth/google/callback`
     - `http://127.0.0.1:3001/auth/google/callback`
   - Copy the Client ID and Client Secret

### Step 2: Update Your .env File

Replace the placeholder values in your `.env` file:

```env
# Replace these with your actual Google OAuth credentials
GOOGLE_CLIENT_ID=your-actual-client-id-from-google-console
GOOGLE_CLIENT_SECRET=your-actual-client-secret-from-google-console
```

### Step 3: Test the Setup

1. **Start the server**:
   ```bash
   npm run start:dev
   ```

2. **Test the Google OAuth flow**:
   - Open browser to: `http://localhost:3001/auth/google`
   - You should be redirected to Google login
   - After login, you should be redirected back to your app

### Example .env File

```env
# Database
MONGODB_URI=mongodb://localhost:27017/wolzyn

# JWT
JWT_SECRET=my-super-secret-jwt-key-for-development

# Google OAuth (REPLACE WITH YOUR ACTUAL VALUES)
GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-abcdefghijklmnopqrstuvwxyz
GOOGLE_CALLBACK_URL=http://localhost:3001/auth/google/callback

# Frontend
FRONTEND_URL=http://localhost:3000

# Server
PORT=3001
```

### Troubleshooting

- **Error persists**: Make sure you restart the server after updating .env
- **Redirect URI mismatch**: Ensure the callback URL in Google Console matches exactly
- **API not enabled**: Make sure Google+ API is enabled in Google Cloud Console

### Security Note

- Never commit your actual `.env` file to version control
- Use different credentials for development and production
- Keep your client secret secure
