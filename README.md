# Wolzyn Backend

A NestJS backend application with Google OAuth authentication using Passport.js.

## Features

- **Google OAuth Authentication**: Secure authentication using Google OAuth 2.0
- **JWT Tokens**: Stateless authentication with JSON Web Tokens
- **PostgreSQL Integration**: User data persistence with Prisma ORM
- **TypeScript**: Full TypeScript support for type safety
- **Modern NestJS**: Built with NestJS 10 and latest best practices

## API Endpoints

### Authentication Routes

- `GET /auth/google` - Initiates Google OAuth flow
- `GET /auth/google/callback` - Google OAuth callback handler
- `GET /auth/profile` - Get authenticated user profile (requires JWT)
- `POST /auth/logout` - Logout endpoint

## Setup Instructions

### 1. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Update the `.env` file with your actual values:

```env
# Database
DATABASE_URL="postgresql://postgres:wolzyn@localhost:5432/wolzyn?schema=public"

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Google OAuth (Get these from Google Cloud Console)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3001/auth/google/callback

# Frontend
FRONTEND_URL=http://localhost:3000

# Server
PORT=3001
```

### 2. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API
4. Go to "Credentials" and create OAuth 2.0 Client IDs
5. Add authorized redirect URIs:
   - `http://localhost:3001/auth/google/callback` (for development)
6. Copy the Client ID and Client Secret to your `.env` file

### 3. Database Setup

Make sure PostgreSQL is running on your system:

```bash
# Using PostgreSQL locally (make sure it's installed)
# Create database and user
psql -U postgres
CREATE DATABASE wolzyn;
CREATE USER postgres WITH PASSWORD 'wolzyn';
GRANT ALL PRIVILEGES ON DATABASE wolzyn TO postgres;

# Or using Docker
docker run -d \
  --name postgres \
  -e POSTGRES_DB=wolzyn \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=wolzyn \
  -p 5432:5432 \
  postgres:15
```

### 4. Setup Database Schema

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push
```

### 5. Install Dependencies

```bash
npm install
```

### 6. Run the Application

```bash
# Development mode with hot reload
npm run start:dev

# Production build
npm run build
npm run start:prod
```

The server will start on `http://localhost:3001`

## Usage

### Authentication Flow

1. **Initiate Login**: Direct users to `GET /auth/google`
2. **Google OAuth**: User authenticates with Google
3. **Callback**: Google redirects to `/auth/google/callback`
4. **Token Generation**: Server generates JWT and redirects to frontend with token
5. **Protected Routes**: Use JWT token in Authorization header for protected routes

### Frontend Integration

```javascript
// Initiate Google login
window.location.href = 'http://localhost:3001/auth/google';

// Handle successful authentication (on your frontend success page)
const urlParams = new URLSearchParams(window.location.search);
const token = urlParams.get('token');
if (token) {
  localStorage.setItem('authToken', token);
  // Redirect to dashboard or main app
}

// Make authenticated requests
const response = await fetch('http://localhost:3001/auth/profile', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
  }
});
```

## Project Structure

```
src/
├── auth/
│   ├── guards/
│   │   ├── google-auth.guard.ts
│   │   └── jwt-auth.guard.ts
│   ├── strategies/
│   │   ├── google.strategy.ts
│   │   └── jwt.strategy.ts
│   ├── auth.controller.ts
│   ├── auth.module.ts
│   └── auth.service.ts
├── prisma/
│   ├── prisma.module.ts
│   └── prisma.service.ts
├── app.module.ts
└── main.ts
prisma/
└── schema.prisma
```

## Development

```bash
# Run in development mode
npm run start:dev

# Build the project
npm run build

# Lint the code
npm run lint

# Format the code
npm run format

# Database management
npm run db:generate    # Generate Prisma client
npm run db:push        # Push schema changes to database
npm run db:migrate     # Create and run migrations
npm run db:studio      # Open Prisma Studio (database GUI)
```

## License

MIT
