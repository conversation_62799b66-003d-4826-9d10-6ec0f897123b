{"name": "wolzyn-backend", "version": "1.0.0", "description": "NestJS backend for Wolzyn application", "author": "", "private": true, "license": "MIT", "engines": {"node": ">=18.0.0"}, "scripts": {"start": "node dist/main.js", "start:dev": "nest start --watch", "build": "nest build", "start:prod": "node dist/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\""}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/mongoose": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/jwt": "^10.1.0", "mongoose": "^7.5.0", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "dotenv": "^16.3.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@types/passport": "^1.0.12", "@types/passport-google-oauth20": "^2.0.11", "@types/passport-jwt": "^3.0.9", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "prettier": "^3.0.0", "ts-node": "^10.9.0", "ts-node-dev": "^2.0.0", "typescript": "^5.1.3"}}